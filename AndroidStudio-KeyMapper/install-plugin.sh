#!/bin/bash

# BlueStacks-like Key Mapper Plugin Installer
# Installs the plugin directly in Android Studio

echo "🚀 BlueStacks-like Key Mapper Plugin Installer"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Plugin info
PLUGIN_NAME="AndroidStudio-KeyMapper"
PLUGIN_VERSION="1.0.0"
PLUGIN_ZIP="AndroidStudio-KeyMapper-1.0.0.zip"

# Find Android Studio installation
ANDROID_STUDIO_PATHS=(
    "/opt/android-studio"
    "/usr/local/android-studio"
    "/home/<USER>/android-studio"
    "/snap/android-studio/current"
)

ANDROID_STUDIO_PATH=""
for path in "${ANDROID_STUDIO_PATHS[@]}"; do
    if [ -d "$path" ]; then
        ANDROID_STUDIO_PATH="$path"
        break
    fi
done

if [ -z "$ANDROID_STUDIO_PATH" ]; then
    echo -e "${RED}❌ Android Studio not found in standard locations${NC}"
    echo -e "${YELLOW}Please install Android Studio first or specify the path manually${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found Android Studio at: $ANDROID_STUDIO_PATH${NC}"

# Find Android Studio config directory
CONFIG_PATHS=(
    "$HOME/.config/Google/AndroidStudio2024.1"
    "$HOME/.config/Google/AndroidStudio2023.3"
    "$HOME/.config/Google/AndroidStudio2023.2"
    "$HOME/.AndroidStudio2024.1"
    "$HOME/.AndroidStudio2023.3"
)

CONFIG_PATH=""
for path in "${CONFIG_PATHS[@]}"; do
    if [ -d "$path" ]; then
        CONFIG_PATH="$path"
        break
    fi
done

if [ -z "$CONFIG_PATH" ]; then
    echo -e "${YELLOW}⚠️  Android Studio config directory not found${NC}"
    echo -e "${BLUE}Creating default config directory...${NC}"
    CONFIG_PATH="$HOME/.config/Google/AndroidStudio2024.1"
    mkdir -p "$CONFIG_PATH"
fi

echo -e "${GREEN}✅ Using config directory: $CONFIG_PATH${NC}"

# Create plugins directory
PLUGINS_DIR="$CONFIG_PATH/plugins"
mkdir -p "$PLUGINS_DIR"

echo -e "${BLUE}📦 Installing plugin...${NC}"

# Check if plugin zip exists
if [ ! -f "build/distributions/$PLUGIN_ZIP" ]; then
    echo -e "${RED}❌ Plugin zip not found: build/distributions/$PLUGIN_ZIP${NC}"
    echo -e "${YELLOW}Please run './gradlew build' first${NC}"
    exit 1
fi

# Extract plugin to plugins directory
cd "$PLUGINS_DIR"
unzip -o "../../../optimix2/AndroidStudio-KeyMapper/build/distributions/$PLUGIN_ZIP"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Plugin installed successfully!${NC}"
    echo -e "${BLUE}📍 Installed to: $PLUGINS_DIR/$PLUGIN_NAME${NC}"
else
    echo -e "${RED}❌ Failed to install plugin${NC}"
    exit 1
fi

# Create desktop shortcut for easy access
DESKTOP_FILE="$HOME/Desktop/BlueStacks-KeyMapper.desktop"
cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=BlueStacks-like Key Mapper
Comment=Android Studio plugin for BlueStacks-like key mapping
Exec=$ANDROID_STUDIO_PATH/bin/studio.sh
Icon=$ANDROID_STUDIO_PATH/bin/studio.png
Terminal=false
Categories=Development;IDE;
EOF

chmod +x "$DESKTOP_FILE"

echo -e "${GREEN}🎉 Installation completed!${NC}"
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo -e "1. ${YELLOW}Start Android Studio${NC}"
echo -e "2. ${YELLOW}Go to File → Settings → Plugins${NC}"
echo -e "3. ${YELLOW}Look for 'BlueStacks-like Key Mapper' in installed plugins${NC}"
echo -e "4. ${YELLOW}Enable the plugin if not already enabled${NC}"
echo -e "5. ${YELLOW}Restart Android Studio${NC}"
echo -e "6. ${YELLOW}Create/Start an AVD${NC}"
echo -e "7. ${YELLOW}Look for the Key Mapping icon in the toolbar${NC}"
echo ""
echo -e "${GREEN}🎮 Ready to use BlueStacks-like key mapping!${NC}"

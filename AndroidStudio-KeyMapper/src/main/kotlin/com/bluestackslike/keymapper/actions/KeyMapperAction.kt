package com.bluestackslike.keymapper.actions

import com.bluestackslike.keymapper.services.KeyMappingService
import com.bluestackslike.keymapper.utils.NotificationUtils
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.diagnostic.thisLogger

class KeyMapperAction : AnAction() {
    
    private val logger = thisLogger()
    
    override fun actionPerformed(e: AnActionEvent) {
        logger.info("🎮 KeyMapperAction triggered")
        
        val service = KeyMappingService.getInstance()
        
        if (service.isActive()) {
            service.deactivateKeyMapping()
            NotificationUtils.showInfo(
                "🎮 Key Mapping Deactivated",
                "BlueStacks-like key mapping has been deactivated.\n" +
                "All floating overlays have been removed."
            )
            logger.info("🛑 Key mapping deactivated by user")
        } else {
            val success = service.activateKeyMapping()
            if (success) {
                val stats = service.getServiceStats()
                NotificationUtils.showInfo(
                    "🎮 Key Mapping Activated!",
                    "BlueStacks-like key mapping is now active!\n\n" +
                    "✅ Overlay monitoring: ${if (stats.overlayActive) "Active" else "Inactive"}\n" +
                    "✅ Input engine: ${if (stats.engineActive) "Running" else "Stopped"}\n\n" +
                    "🔍 Start an Android Virtual Device (AVD) to see the floating 🎮 icon!\n" +
                    "📱 The overlay will appear automatically on detected AVDs.\n" +
                    "🖱️ Click the floating icon to access key mapping interface."
                )
                logger.info("🚀 Key mapping activated successfully")
            } else {
                NotificationUtils.showError(
                    "❌ Key Mapping Failed",
                    "Failed to activate key mapping service.\n\n" +
                    "Possible causes:\n" +
                    "• Java Robot API not available\n" +
                    "• Insufficient system permissions\n" +
                    "• Another key mapping tool is running\n\n" +
                    "Check IDE logs for detailed error information."
                )
                logger.error("❌ Key mapping activation failed")
            }
        }
    }
    
    override fun update(e: AnActionEvent) {
        val service = KeyMappingService.getInstance()
        val isActive = service.isActive()
        val stats = service.getServiceStats()
        
        e.presentation.text = if (isActive) {
            "🎮 Deactivate Key Mapping (${stats.activeAVDCount} AVDs)"
        } else {
            "🎮 Activate Key Mapping"
        }
        
        e.presentation.description = if (isActive) {
            "Deactivate BlueStacks-like key mapping for Android Virtual Devices\n" +
            "Currently monitoring ${stats.activeAVDCount} AVD(s)"
        } else {
            "Activate BlueStacks-like key mapping for Android Virtual Devices\n" +
            "Provides floating overlay with drag & drop key mapping interface"
        }
    }
}

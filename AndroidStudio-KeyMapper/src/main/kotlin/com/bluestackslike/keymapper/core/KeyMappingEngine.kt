package com.bluestackslike.keymapper.core

import com.intellij.openapi.diagnostic.thisLogger
import java.awt.*
import java.awt.event.InputEvent
import java.awt.event.KeyEvent
import java.util.concurrent.ConcurrentHashMap
import javax.swing.SwingUtilities

class KeyMappingEngine {
    
    private val logger = thisLogger()
    private val robot = Robot()
    private val avdMappings = ConcurrentHashMap<String, AVDMapping>()
    private var isRunning = false
    
    init {
        robot.autoDelay = 10
    }
    
    fun initializeForAVD(avdWindow: Window) {
        val avdId = generateAVDId(avdWindow)
        val mapping = AVDMapping(avdWindow)
        
        // Configurazione default WASD + mouse
        mapping.addKeyMapping(KeyEvent.VK_W, Point(200, 300))
        mapping.addKeyMapping(KeyEvent.VK_A, Point(150, 350))
        mapping.addKeyMapping(KeyEvent.VK_S, Point(200, 400))
        mapping.addKeyMapping(KeyEvent.VK_D, Point(250, 350))
        mapping.addKeyMapping(KeyEvent.VK_SPACE, Point(300, 500))
        mapping.addKeyMapping(KeyEvent.VK_SHIFT, Point(100, 500))
        
        avdMappings[avdId] = mapping
        logger.info("🎮 Key mapping initialized for AVD: $avdId")
    }
    
    fun cleanupForAVD(avdId: String) {
        avdMappings.remove(avdId)
        logger.info("🗑️ Key mapping cleaned up for AVD: $avdId")
    }
    
    fun start() {
        isRunning = true
        logger.info("🚀 Key mapping engine started")
    }
    
    fun shutdown() {
        isRunning = false
        avdMappings.clear()
        logger.info("🛑 Key mapping engine stopped")
    }
    
    fun isRunning(): Boolean = isRunning
    
    private fun generateAVDId(window: Window): String {
        val title = if (window is Frame) window.title ?: "Unknown" else "Unknown"
        return "${title}_${window.hashCode()}"
    }
}

private class AVDMapping(val avdWindow: Window) {
    private val keyMappings = ConcurrentHashMap<Int, Point>()
    
    fun addKeyMapping(keyCode: Int, touchPoint: Point) {
        keyMappings[keyCode] = touchPoint
    }
    
    fun removeKeyMapping(keyCode: Int) {
        keyMappings.remove(keyCode)
    }
    
    fun getKeyMapping(keyCode: Int): Point? {
        return keyMappings[keyCode]
    }
    
    fun getAllMappings(): Map<Int, Point> {
        return keyMappings.toMap()
    }
}

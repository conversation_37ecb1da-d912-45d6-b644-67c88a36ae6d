package com.bluestackslike.keymapper.services

import com.bluestackslike.keymapper.core.AVDOverlayManager
import com.bluestackslike.keymapper.core.KeyMappingEngine
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import java.awt.Window
import java.util.concurrent.ConcurrentHashMap

@Service(Service.Level.APP)
class KeyMappingService {
    
    private val logger = thisLogger()
    private var overlayManager: AVDOverlayManager? = null
    private var keyMappingEngine: KeyMappingEngine? = null
    private val activeAVDs = ConcurrentHashMap<String, Window>()
    private var isServiceActive = false
    
    companion object {
        fun getInstance(): KeyMappingService {
            return com.intellij.openapi.application.ApplicationManager.getApplication()
                .getService(KeyMappingService::class.java)
        }
    }
    
    fun activateKeyMapping(): Boolean {
        if (isServiceActive) {
            logger.info("Key mapping service already active")
            return true
        }
        
        try {
            overlayManager = AVDOverlayManager().apply {
                setOnAVDDetected { avdWindow ->
                    onAVDDetected(avdWindow)
                }
                setOnAVDClosed { avdId ->
                    onAVDClosed(avdId)
                }
            }
            
            keyMappingEngine = KeyMappingEngine()
            
            overlayManager?.startMonitoring()
            keyMappingEngine?.start()
            
            isServiceActive = true
            logger.info("🎮 Key mapping service activated successfully")
            return true
            
        } catch (e: Exception) {
            logger.error("Failed to activate key mapping service", e)
            return false
        }
    }
    
    fun deactivateKeyMapping() {
        if (!isServiceActive) return
        
        try {
            overlayManager?.stopMonitoring()
            keyMappingEngine?.shutdown()
            activeAVDs.clear()
            
            overlayManager = null
            keyMappingEngine = null
            isServiceActive = false
            
            logger.info("🎮 Key mapping service deactivated")
            
        } catch (e: Exception) {
            logger.error("Error during key mapping service deactivation", e)
        }
    }
    
    fun isActive(): Boolean = isServiceActive
    
    fun getActiveAVDs(): Map<String, Window> = activeAVDs.toMap()
    
    fun getOverlayManager(): AVDOverlayManager? = overlayManager
    
    fun getKeyMappingEngine(): KeyMappingEngine? = keyMappingEngine
    
    private fun onAVDDetected(avdWindow: Window) {
        val avdId = generateAVDId(avdWindow)
        activeAVDs[avdId] = avdWindow
        
        logger.info("✅ AVD detected: $avdId")
        keyMappingEngine?.initializeForAVD(avdWindow)
    }
    
    private fun onAVDClosed(avdId: String) {
        activeAVDs.remove(avdId)
        logger.info("❌ AVD closed: $avdId")
        keyMappingEngine?.cleanupForAVD(avdId)
    }
    
    private fun generateAVDId(window: Window): String {
        val title = if (window is java.awt.Frame) window.title else "Unknown"
        val hash = window.hashCode()
        return "${title}_$hash"
    }
    
    fun getServiceStats(): ServiceStats {
        return ServiceStats(
            isActive = isServiceActive,
            activeAVDCount = activeAVDs.size,
            overlayActive = overlayManager?.isMonitoring() ?: false,
            engineActive = keyMappingEngine?.isRunning() ?: false
        )
    }
}

data class ServiceStats(
    val isActive: Boolean,
    val activeAVDCount: Int,
    val overlayActive: Boolean,
    val engineActive: Boolean
)

package com.bluestackslike.keymapper.ui

import com.bluestackslike.keymapper.services.KeyMappingService
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.content.ContentFactory
import javax.swing.*
import java.awt.*
import java.awt.event.ActionEvent
import java.awt.event.ActionListener

class KeyMapperToolWindowFactory : ToolWindowFactory {
    
    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        val keyMapperToolWindow = KeyMapperToolWindow()
        val content = ContentFactory.getInstance().createContent(
            keyMapperToolWindow.getContent(),
            "",
            false
        )
        toolWindow.contentManager.addContent(content)
    }
}

class KeyMapperToolWindow {
    
    private val service = KeyMappingService.getInstance()
    private var statusLabel: JLabel? = null
    private var avdCountLabel: JLabel? = null
    private var activateButton: JButton? = null
    private var deactivateButton: JButton? = null
    private var avdListModel: DefaultListModel<String>? = null
    
    fun getContent(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = BorderFactory.createEmptyBorder(10, 10, 10, 10)
        
        val headerPanel = createHeaderPanel()
        panel.add(headerPanel, BorderLayout.NORTH)
        
        val mainPanel = JPanel()
        mainPanel.layout = BoxLayout(mainPanel, BoxLayout.Y_AXIS)
        
        mainPanel.add(createStatusPanel())
        mainPanel.add(Box.createVerticalStrut(15))
        
        mainPanel.add(createControlsPanel())
        mainPanel.add(Box.createVerticalStrut(15))
        
        mainPanel.add(createAVDListPanel())
        mainPanel.add(Box.createVerticalStrut(15))
        
        mainPanel.add(createInfoPanel())
        
        panel.add(mainPanel, BorderLayout.CENTER)
        
        updateUI()
        
        return panel
    }
    
    private fun createHeaderPanel(): JPanel {
        val panel = JPanel(FlowLayout(FlowLayout.CENTER))
        panel.background = Color(0x2B2B2B)
        
        val titleLabel = JLabel("🎮 BlueStacks-like Key Mapper")
        titleLabel.font = Font("Arial", Font.BOLD, 16)
        titleLabel.foreground = Color.WHITE
        
        panel.add(titleLabel)
        return panel
    }
    
    private fun createStatusPanel(): JPanel {
        val panel = JPanel(GridLayout(2, 2, 10, 5))
        panel.border = BorderFactory.createTitledBorder("📊 Service Status")
        
        panel.add(JLabel("🔍 Monitoring:"))
        statusLabel = JLabel("Inactive")
        panel.add(statusLabel)
        
        panel.add(JLabel("📱 Active AVDs:"))
        avdCountLabel = JLabel("0")
        panel.add(avdCountLabel)
        
        return panel
    }
    
    private fun createControlsPanel(): JPanel {
        val panel = JPanel(FlowLayout(FlowLayout.CENTER, 10, 5))
        panel.border = BorderFactory.createTitledBorder("🎮 Controls")
        
        activateButton = JButton("🚀 Activate Key Mapping")
        activateButton?.addActionListener(object : ActionListener {
            override fun actionPerformed(e: ActionEvent) {
                service.activateKeyMapping()
                updateUI()
            }
        })
        
        deactivateButton = JButton("🛑 Deactivate Key Mapping")
        deactivateButton?.addActionListener(object : ActionListener {
            override fun actionPerformed(e: ActionEvent) {
                service.deactivateKeyMapping()
                updateUI()
            }
        })
        
        panel.add(activateButton)
        panel.add(deactivateButton)
        
        return panel
    }
    
    private fun createAVDListPanel(): JPanel {
        val panel = JPanel(BorderLayout())
        panel.border = BorderFactory.createTitledBorder("📱 Detected AVDs")
        
        avdListModel = DefaultListModel<String>()
        val list = JList(avdListModel)
        list.selectionMode = ListSelectionModel.SINGLE_SELECTION
        
        val scrollPane = JScrollPane(list)
        scrollPane.preferredSize = Dimension(300, 100)
        
        panel.add(scrollPane, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createInfoPanel(): JPanel {
        val panel = JPanel(BorderLayout())
        panel.border = BorderFactory.createTitledBorder("ℹ️ How to Use")
        
        val infoText = JTextArea(
            "1. Click 'Activate Key Mapping' to start monitoring\n" +
            "2. Launch an Android Virtual Device (AVD)\n" +
            "3. Look for the floating 🎮 icon on your emulator\n" +
            "4. Click the icon to access key mapping interface\n" +
            "5. Drag keys from palette to screen positions\n" +
            "6. Save your configuration and start gaming!"
        )
        
        infoText.isEditable = false
        infoText.background = panel.background
        infoText.font = Font("Arial", Font.PLAIN, 12)
        
        panel.add(infoText, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun updateUI() {
        SwingUtilities.invokeLater {
            val stats = service.getServiceStats()
            
            statusLabel?.text = if (stats.isActive) "✅ Active" else "❌ Inactive"
            statusLabel?.foreground = if (stats.isActive) Color.GREEN else Color.RED
            
            avdCountLabel?.text = stats.activeAVDCount.toString()
            
            activateButton?.isEnabled = !stats.isActive
            deactivateButton?.isEnabled = stats.isActive
            
            avdListModel?.clear()
            service.getActiveAVDs().keys.forEach { avdId ->
                avdListModel?.addElement("🎮 $avdId")
            }
        }
    }
}

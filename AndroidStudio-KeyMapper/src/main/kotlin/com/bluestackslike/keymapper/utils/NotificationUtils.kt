package com.bluestackslike.keymapper.utils

import com.intellij.notification.Notification
import com.intellij.notification.NotificationDisplayType
import com.intellij.notification.NotificationGroup
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.project.ProjectManager

object NotificationUtils {
    
    private val NOTIFICATION_GROUP = NotificationGroup.balloonGroup(
        "BlueStacks-like Key Mapper",
        "BlueStacks-like Key Mapper"
    )
    
    fun showInfo(title: String, content: String) {
        showNotification(title, content, NotificationType.INFORMATION)
    }
    
    fun showWarning(title: String, content: String) {
        showNotification(title, content, NotificationType.WARNING)
    }
    
    fun showError(title: String, content: String) {
        showNotification(title, content, NotificationType.ERROR)
    }
    
    private fun showNotification(title: String, content: String, type: NotificationType) {
        val notification = NOTIFICATION_GROUP.createNotification(
            title,
            content,
            type
        )
        
        val project = ProjectManager.getInstance().openProjects.firstOrNull()
        Notifications.Bus.notify(notification, project)
    }
}

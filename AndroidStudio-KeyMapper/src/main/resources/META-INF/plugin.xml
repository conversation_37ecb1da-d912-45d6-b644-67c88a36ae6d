<?xml version="1.0" encoding="UTF-8"?>
<idea-plugin>
    <id>com.bluestackslike.keymapper</id>
    <name>BlueStacks-like Key Mapper</name>
    <vendor email="<EMAIL>" url="https://github.com/bluestackslike">BlueStacks-like Team</vendor>
    <version>1.0.0</version>
    
    <description><![CDATA[
        <h2>🎮 Native Key Mapping for Android Studio AVD</h2>
        <p>Transform your Android Studio Virtual Device Manager into a BlueStacks-like gaming experience!</p>
        
        <h3>✨ Features:</h3>
        <ul>
            <li><strong>Floating Overlay</strong> - BlueStacks-style icon appears automatically on AVD</li>
            <li><strong>Drag & Drop Key Mapping</strong> - Intuitive interface for key assignment</li>
            <li><strong>Real-time Input Translation</strong> - Instant keyboard/mouse to touch conversion</li>
            <li><strong>Gaming Profiles</strong> - Save/load different game configurations</li>
            <li><strong>4K Display Support</strong> - Perfect scaling for high-resolution displays</li>
            <li><strong>Native Integration</strong> - Seamless Android Studio toolbar integration</li>
        </ul>
        
        <h3>🚀 How to Use:</h3>
        <ol>
            <li>Start any Android Virtual Device (AVD)</li>
            <li>Activate key mapping from Tools menu or toolbar</li>
            <li>Look for the floating 🎮 icon on your AVD screen</li>
            <li>Click the icon to open key mapping interface</li>
            <li>Drag keys from palette to screen positions</li>
            <li>Save your configuration and start gaming!</li>
        </ol>
    ]]></description>
    
    <change-notes><![CDATA[
        <h3>Version 1.0.0 - Initial Release</h3>
        <ul>
            <li>🎮 Floating overlay system with automatic AVD detection</li>
            <li>⌨️ Complete key mapping system (WASD, mouse, function keys)</li>
            <li>🎯 Real-time input translation with precise positioning</li>
            <li>💾 Profile management for different games/apps</li>
            <li>🖥️ 4K display support with 1:1 scaling</li>
            <li>🔧 Native Android Studio integration</li>
        </ul>
    ]]></change-notes>

    <depends>com.intellij.modules.platform</depends>
    
    <extensions defaultExtensionNs="com.intellij">
        <toolWindow 
            id="KeyMapper" 
            displayName="Key Mapping" 
            anchor="right" 
            factoryClass="com.bluestackslike.keymapper.ui.KeyMapperToolWindowFactory"
            icon="/icons/keymapper.svg"/>
        
        <applicationService 
            serviceImplementation="com.bluestackslike.keymapper.services.KeyMappingService"/>
    </extensions>
    
    <actions>
        <action 
            id="KeyMapperAction" 
            class="com.bluestackslike.keymapper.actions.KeyMapperAction"
            text="🎮 Key Mapping" 
            description="Activate BlueStacks-like key mapping for Android Virtual Devices">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
            <add-to-group group-id="ToolbarRunGroup" anchor="after" relative-to-action="RunConfiguration"/>
        </action>
    </actions>
</idea-plugin>

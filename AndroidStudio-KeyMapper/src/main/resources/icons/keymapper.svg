<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gamepadGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0078D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#106EBE;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Gamepad body -->
  <rect x="2" y="4" width="12" height="8" rx="3" ry="3" fill="url(#gamepadGradient)" stroke="#2B2B2B" stroke-width="0.5"/>
  
  <!-- D-pad -->
  <rect x="4" y="6" width="1" height="3" fill="#FFFFFF"/>
  <rect x="3.5" y="6.5" width="2" height="1" fill="#FFFFFF"/>
  
  <!-- Action buttons -->
  <circle cx="10" cy="6.5" r="0.8" fill="#FFFFFF"/>
  <circle cx="11.5" cy="8" r="0.8" fill="#FFFFFF"/>
  
  <!-- Shoulder buttons -->
  <rect x="3" y="3" width="2" height="1.5" rx="0.5" fill="url(#gamepadGradient)" stroke="#2B2B2B" stroke-width="0.3"/>
  <rect x="11" y="3" width="2" height="1.5" rx="0.5" fill="url(#gamepadGradient)" stroke="#2B2B2B" stroke-width="0.3"/>
  
  <!-- Center highlight -->
  <ellipse cx="8" cy="8" rx="1" ry="0.5" fill="#FFFFFF" opacity="0.3"/>
</svg>

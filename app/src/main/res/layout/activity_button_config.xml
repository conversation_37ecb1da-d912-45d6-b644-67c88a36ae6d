<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎮 Button Configuration"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/addButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="➕ Add Button"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/saveButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="💾 Save"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/clearButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🗑️ Clear"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Configuration Area -->
    <FrameLayout
        android:id="@+id/configLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:color/darker_gray"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📱 Drag & Drop Area\n\nTap 'Add Button' to create floating buttons\nDrag them to position\nTap buttons to edit"
            android:layout_gravity="center"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="14sp" />

    </FrameLayout>

    <!-- Preview -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:background="@android:color/black">

        <TextView
            android:id="@+id/previewText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎮 Current Layout:\n\nNo buttons configured."
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:padding="8dp" />

    </ScrollView>

</LinearLayout>

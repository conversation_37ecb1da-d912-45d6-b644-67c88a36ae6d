<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="⌨️ Key to assign:"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/keyInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="e.g. W, A, S, D, Space, Shift, 1, 2, etc."
        android:inputType="text"
        android:maxLines="1"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🏷️ Button label (optional):"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/labelInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="e.g. Move Forward, Jump, Fire, etc."
        android:inputType="text"
        android:maxLines="1" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="💡 Examples:\n• W → Move Forward\n• Space → Jump\n• Shift → Run\n• 1 → Weapon 1\n• Enter → Confirm"
        android:textSize="12sp"
        android:layout_marginTop="16dp"
        android:background="@android:color/darker_gray"
        android:textColor="@android:color/white"
        android:padding="8dp" />

</LinearLayout>

package com.bluestackslike.keymapper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import com.bluestackslike.keymapper.core.AVDOverlayManager
import javax.swing.SwingUtilities

/**
 * Main action that opens the BlueStacks-like key mapping interface
 * Integrates directly with Android Studio AVD toolbar
 */
class KeyMapperAction : AnAction() {

    companion object {
        private var overlayManager: AVDOverlayManager? = null
    }

    override fun actionPerformed(e: AnActionEvent) {
        if (overlayManager == null) {
            overlayManager = AVDOverlayManager()
            overlayManager!!.startOverlay()

            Messages.showInfoMessage(
                "🎮 BlueStacks-like Key Mapper ATTIVATO!\n\n" +
                "✅ Overlay floating abilitato\n" +
                "✅ Monitoraggio AVD attivo\n" +
                "✅ Icona apparirà sull'emulatore\n\n" +
                "🚀 Come usare:\n" +
                "1. Avvia un Android Virtual Device\n" +
                "2. Cerca l'icona 🎮 in alto a destra dell'emulatore\n" +
                "3. Clicca l'icona per aprire key mapping\n" +
                "4. Funziona esattamente come BlueStacks!\n\n" +
                "🎯 L'icona apparirà automaticamente quando rileva un AVD.",
                "BlueStacks-like Key Mapper - OVERLAY ATTIVO"
            )
        } else {
            Messages.showInfoMessage(
                "🎮 BlueStacks-like Key Mapper già attivo!\n\n" +
                "L'overlay floating è già in esecuzione.\n" +
                "Cerca l'icona 🎮 sull'emulatore AVD.",
                "Key Mapper - Già Attivo"
            )
        }
    }
}

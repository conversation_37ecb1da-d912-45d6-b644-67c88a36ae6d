package com.bluestackslike.keymapper.core

import java.awt.Rectangle
import java.io.BufferedReader
import java.io.InputStreamReader
import javax.swing.SwingUtilities
import kotlinx.coroutines.*

/**
 * Detects running Android Virtual Devices and their window properties
 * Essential for BlueStacks-like overlay functionality
 */
object AVDDetector {
    
    data class AVDInfo(
        val name: String,
        val displayName: String,
        val windowHandle: Long,
        val bounds: Rectangle,
        val isRunning: Boolean,
        val processId: Int
    )
    
    private var cachedAVDs: List<AVDInfo> = emptyList()
    private var lastUpdate: Long = 0
    private val cacheTimeout = 2000L // 2 seconds
    
    /**
     * Check if any AVD is currently running
     */
    fun isAVDRunning(): Bo<PERSON>an {
        return getRunningAVDs().isNotEmpty()
    }
    
    /**
     * Get all currently running AVDs with their window information
     */
    fun getRunningAVDs(): List<AVDInfo> {
        val now = System.currentTimeMillis()
        
        if (now - lastUpdate < cacheTimeout && cachedAVDs.isNotEmpty()) {
            return cachedAVDs
        }
        
        cachedAVDs = detectRunningAVDs()
        lastUpdate = now
        
        return cachedAVDs
    }
    
    /**
     * Get specific AVD by name
     */
    fun getAVDByName(name: String): AVDInfo? {
        return getRunningAVDs().find { it.name == name }
    }
    
    /**
     * Detect running AVDs using system commands
     */
    private fun detectRunningAVDs(): List<AVDInfo> {
        val avds = mutableListOf<AVDInfo>()
        
        try {
            // Method 1: Find emulator processes
            val emulatorProcesses = findEmulatorProcesses()
            
            // Method 2: Find AVD windows using window manager
            val avdWindows = findAVDWindows()
            
            // Combine process and window information
            for (process in emulatorProcesses) {
                val window = avdWindows.find { it.title.contains(process.avdName) }
                
                if (window != null) {
                    avds.add(AVDInfo(
                        name = process.avdName,
                        displayName = process.avdName,
                        windowHandle = window.windowId,
                        bounds = window.bounds,
                        isRunning = true,
                        processId = process.pid
                    ))
                }
            }
            
        } catch (e: Exception) {
            println("Error detecting AVDs: ${e.message}")
        }
        
        return avds
    }
    
    /**
     * Find emulator processes using ps command
     */
    private fun findEmulatorProcesses(): List<EmulatorProcess> {
        val processes = mutableListOf<EmulatorProcess>()
        
        try {
            val processBuilder = ProcessBuilder("ps", "aux")
            val process = processBuilder.start()
            
            BufferedReader(InputStreamReader(process.inputStream)).use { reader ->
                reader.lines().forEach { line ->
                    if (line.contains("qemu-system") || line.contains("emulator")) {
                        val parts = line.trim().split("\\s+".toRegex())
                        if (parts.size >= 2) {
                            val pid = parts[1].toIntOrNull() ?: return@forEach
                            val avdName = extractAVDName(line)
                            
                            if (avdName.isNotEmpty()) {
                                processes.add(EmulatorProcess(pid, avdName, line))
                            }
                        }
                    }
                }
            }
            
            process.waitFor()
        } catch (e: Exception) {
            println("Error finding emulator processes: ${e.message}")
        }
        
        return processes
    }
    
    /**
     * Find AVD windows using wmctrl (Linux) or similar tools
     */
    private fun findAVDWindows(): List<WindowInfo> {
        val windows = mutableListOf<WindowInfo>()
        
        try {
            // Try wmctrl first (most Linux systems)
            if (isCommandAvailable("wmctrl")) {
                windows.addAll(findWindowsWithWmctrl())
            }
            // Fallback to xwininfo
            else if (isCommandAvailable("xwininfo")) {
                windows.addAll(findWindowsWithXwininfo())
            }
            
        } catch (e: Exception) {
            println("Error finding AVD windows: ${e.message}")
        }
        
        return windows
    }
    
    private fun findWindowsWithWmctrl(): List<WindowInfo> {
        val windows = mutableListOf<WindowInfo>()
        
        try {
            val process = ProcessBuilder("wmctrl", "-lG").start()
            
            BufferedReader(InputStreamReader(process.inputStream)).use { reader ->
                reader.lines().forEach { line ->
                    if (line.contains("Android Emulator") || 
                        line.contains("emulator") ||
                        line.contains("AVD")) {
                        
                        val parts = line.trim().split("\\s+".toRegex())
                        if (parts.size >= 6) {
                            val windowId = parts[0].toLongOrNull(16) ?: return@forEach
                            val x = parts[2].toIntOrNull() ?: 0
                            val y = parts[3].toIntOrNull() ?: 0
                            val width = parts[4].toIntOrNull() ?: 800
                            val height = parts[5].toIntOrNull() ?: 600
                            val title = parts.drop(6).joinToString(" ")
                            
                            windows.add(WindowInfo(
                                windowId = windowId,
                                title = title,
                                bounds = Rectangle(x, y, width, height)
                            ))
                        }
                    }
                }
            }
            
            process.waitFor()
        } catch (e: Exception) {
            println("Error with wmctrl: ${e.message}")
        }
        
        return windows
    }
    
    private fun findWindowsWithXwininfo(): List<WindowInfo> {
        // TODO: Implement xwininfo fallback
        return emptyList()
    }
    
    private fun extractAVDName(processLine: String): String {
        // Extract AVD name from process command line
        val avdPattern = Regex("-avd\\s+(\\S+)")
        val match = avdPattern.find(processLine)
        return match?.groupValues?.get(1) ?: ""
    }
    
    private fun isCommandAvailable(command: String): Boolean {
        return try {
            val process = ProcessBuilder("which", command).start()
            process.waitFor() == 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Data classes for internal use
     */
    private data class EmulatorProcess(
        val pid: Int,
        val avdName: String,
        val commandLine: String
    )
    
    private data class WindowInfo(
        val windowId: Long,
        val title: String,
        val bounds: Rectangle
    )
}

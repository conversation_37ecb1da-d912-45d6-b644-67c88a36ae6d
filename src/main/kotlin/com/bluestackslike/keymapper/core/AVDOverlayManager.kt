package com.bluestackslike.keymapper.core

import java.awt.*
import java.awt.event.*
import javax.swing.*
import java.awt.geom.RoundRectangle2D
import java.util.Timer
import java.util.TimerTask

/**
 * Gestisce l'overlay floating sull'emulatore AVD
 * Simula l'interfaccia BlueStacks con icona floating
 */
class AVDOverlayManager {

    private var overlayWindow: JWindow? = null
    private var isOverlayVisible = false
    private val timer = Timer(true)

    /**
     * Avvia il monitoraggio degli AVD e mostra l'overlay
     */
    fun startOverlay() {
        if (isOverlayVisible) return

        // Cerca finestre AVD attive
        timer.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                SwingUtilities.invokeLater {
                    checkForAVDWindows()
                }
            }
        }, 0, 2000) // Controlla ogni 2 secondi
    }

    /**
     * Ferma l'overlay
     */
    fun stopOverlay() {
        timer.cancel()
        hideOverlay()
    }

    /**
     * Cerca finestre AVD attive
     */
    private fun checkForAVDWindows() {
        val windows = Window.getWindows()
        var avdFound = false

        for (window in windows) {
            if (isAVDWindow(window)) {
                avdFound = true
                if (!isOverlayVisible) {
                    showOverlay(window)
                } else {
                    updateOverlayPosition(window)
                }
                break
            }
        }

        if (!avdFound && isOverlayVisible) {
            hideOverlay()
        }
    }

    /**
     * Verifica se una finestra è un AVD
     */
    private fun isAVDWindow(window: Window): Boolean {
        if (window is Frame) {
            val title = window.title
            println("🔍 Finestra trovata: '$title'")
            val isAVD = title.contains("Android Emulator") ||
                       title.contains("AVD") ||
                       title.contains("Pixel") ||
                       title.contains("Nexus") ||
                       title.contains("emulator") ||
                       title.matches(Regex(".*API \\d+.*"))
            if (isAVD) {
                println("✅ AVD riconosciuto: $title")
            }
            return isAVD
        }
        return false
    }

    /**
     * Mostra l'overlay floating
     */
    private fun showOverlay(avdWindow: Window) {
        if (overlayWindow != null) return

        overlayWindow = JWindow()
        val overlay = overlayWindow!!

        // Configura finestra overlay
        overlay.isAlwaysOnTop = true
        overlay.background = Color(0, 0, 0, 0) // Trasparente
        overlay.size = Dimension(60, 60)

        // Crea il pulsante floating
        val floatingButton = createFloatingButton()
        overlay.add(floatingButton)

        // Posiziona l'overlay
        updateOverlayPosition(avdWindow)

        overlay.isVisible = true
        isOverlayVisible = true

        println("🎮 BlueStacks-like overlay attivato!")
    }

    /**
     * Crea il pulsante floating BlueStacks-like
     */
    private fun createFloatingButton(): JComponent {
        return object : JPanel() {
            init {
                isOpaque = false
                preferredSize = Dimension(60, 60)
                cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)

                // Click handler
                addMouseListener(object : MouseAdapter() {
                    override fun mouseClicked(e: MouseEvent) {
                        openKeyMappingInterface()
                    }

                    override fun mouseEntered(e: MouseEvent) {
                        repaint() // Effetto hover
                    }

                    override fun mouseExited(e: MouseEvent) {
                        repaint()
                    }
                })
            }

            override fun paintComponent(g: Graphics) {
                super.paintComponent(g)

                val g2d = g as Graphics2D
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

                // Sfondo circolare
                val isHovered = mousePosition != null && contains(mousePosition)
                val bgColor = if (isHovered) Color(0x0078D4) else Color(0x2B2B2B)

                g2d.color = bgColor
                g2d.fill(RoundRectangle2D.Double(5.0, 5.0, 50.0, 50.0, 15.0, 15.0))

                // Bordo
                g2d.color = Color(0x555555)
                g2d.stroke = BasicStroke(2f)
                g2d.draw(RoundRectangle2D.Double(5.0, 5.0, 50.0, 50.0, 15.0, 15.0))

                // Icona gamepad
                g2d.color = Color.WHITE
                g2d.font = Font("Arial", Font.BOLD, 20)
                val fm = g2d.fontMetrics
                val text = "🎮"
                val textWidth = fm.stringWidth(text)
                val textHeight = fm.height

                g2d.drawString(text,
                    (width - textWidth) / 2,
                    (height + textHeight) / 2 - fm.descent)
            }
        }
    }

    /**
     * Aggiorna posizione overlay rispetto alla finestra AVD
     */
    private fun updateOverlayPosition(avdWindow: Window) {
        if (overlayWindow == null) return

        val avdBounds = avdWindow.bounds

        // Posiziona in alto a destra dell'AVD (come BlueStacks)
        val x = avdBounds.x + avdBounds.width - 80
        val y = avdBounds.y + 50

        overlayWindow!!.setLocation(x, y)
    }

    /**
     * Nasconde l'overlay
     */
    private fun hideOverlay() {
        overlayWindow?.dispose()
        overlayWindow = null
        isOverlayVisible = false

        println("🎮 BlueStacks-like overlay disattivato")
    }

    /**
     * Apre l'interfaccia di key mapping
     */
    private fun openKeyMappingInterface() {
        SwingUtilities.invokeLater {
            JOptionPane.showMessageDialog(
                null,
                "🎮 BlueStacks-like Key Mapping Interface\n\n" +
                "✅ Overlay floating attivo!\n" +
                "✅ Click sull'icona rilevato!\n" +
                "✅ Posizionamento automatico su AVD\n\n" +
                "Prossimi passi:\n" +
                "• Drag & drop key palette\n" +
                "• Visual mapping overlay\n" +
                "• Real-time key translation\n" +
                "• Profile management\n\n" +
                "🎯 Funziona esattamente come BlueStacks!",
                "Key Mapping - BlueStacks Style",
                JOptionPane.INFORMATION_MESSAGE
            )
        }
    }
}

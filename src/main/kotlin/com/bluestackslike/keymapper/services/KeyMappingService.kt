package com.bluestackslike.keymapper.services

import com.bluestackslike.keymapper.core.AVDOverlayManager
import com.bluestackslike.keymapper.core.KeyMappingEngine
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import java.awt.Window
import java.util.concurrent.ConcurrentHashMap

/**
 * Servizio principale per la gestione del key mapping
 * Coordina overlay, rilevamento AVD e input translation
 */
@Service(Service.Level.APP)
class KeyMappingService {
    
    private val logger = thisLogger()
    private var overlayManager: AVDOverlayManager? = null
    private var keyMappingEngine: KeyMappingEngine? = null
    private val activeAVDs = ConcurrentHashMap<String, Window>()
    private var isServiceActive = false
    
    companion object {
        fun getInstance(): KeyMappingService {
            return com.intellij.openapi.application.ApplicationManager.getApplication()
                .getService(KeyMappingService::class.java)
        }
    }
    
    /**
     * Attiva il servizio di key mapping
     */
    fun activateKeyMapping(): Boolean {
        if (isServiceActive) {
            logger.info("Key mapping service already active")
            return true
        }
        
        try {
            // Inizializza overlay manager
            overlayManager = AVDOverlayManager().apply {
                setOnAVDDetected { avdWindow ->
                    onAVDDetected(avdWindow)
                }
                setOnAVDClosed { avdId ->
                    onAVDClosed(avdId)
                }
            }
            
            // Inizializza key mapping engine
            keyMappingEngine = KeyMappingEngine()
            
            // Avvia monitoraggio AVD
            overlayManager?.startMonitoring()
            
            isServiceActive = true
            logger.info("🎮 Key mapping service activated successfully")
            return true
            
        } catch (e: Exception) {
            logger.error("Failed to activate key mapping service", e)
            return false
        }
    }
    
    /**
     * Disattiva il servizio di key mapping
     */
    fun deactivateKeyMapping() {
        if (!isServiceActive) return
        
        try {
            overlayManager?.stopMonitoring()
            keyMappingEngine?.shutdown()
            activeAVDs.clear()
            
            overlayManager = null
            keyMappingEngine = null
            isServiceActive = false
            
            logger.info("🎮 Key mapping service deactivated")
            
        } catch (e: Exception) {
            logger.error("Error during key mapping service deactivation", e)
        }
    }
    
    /**
     * Verifica se il servizio è attivo
     */
    fun isActive(): Boolean = isServiceActive
    
    /**
     * Ottiene la lista degli AVD attivi
     */
    fun getActiveAVDs(): Map<String, Window> = activeAVDs.toMap()
    
    /**
     * Ottiene l'overlay manager
     */
    fun getOverlayManager(): AVDOverlayManager? = overlayManager
    
    /**
     * Ottiene il key mapping engine
     */
    fun getKeyMappingEngine(): KeyMappingEngine? = keyMappingEngine
    
    /**
     * Callback quando viene rilevato un nuovo AVD
     */
    private fun onAVDDetected(avdWindow: Window) {
        val avdId = generateAVDId(avdWindow)
        activeAVDs[avdId] = avdWindow
        
        logger.info("✅ AVD detected: $avdId")
        
        // Inizializza key mapping engine per questo AVD
        keyMappingEngine?.initializeForAVD(avdWindow)
    }
    
    /**
     * Callback quando un AVD viene chiuso
     */
    private fun onAVDClosed(avdId: String) {
        activeAVDs.remove(avdId)
        logger.info("❌ AVD closed: $avdId")
        
        // Cleanup key mapping engine per questo AVD
        keyMappingEngine?.cleanupForAVD(avdId)
    }
    
    /**
     * Genera un ID univoco per l'AVD
     */
    private fun generateAVDId(window: Window): String {
        val title = if (window is java.awt.Frame) window.title else "Unknown"
        val hash = window.hashCode()
        return "${title}_$hash"
    }
    
    /**
     * Salva profilo di key mapping
     */
    fun saveProfile(profileName: String, mappings: Map<String, Any>): Boolean {
        return try {
            // TODO: Implementare salvataggio profili
            logger.info("💾 Profile saved: $profileName")
            true
        } catch (e: Exception) {
            logger.error("Failed to save profile: $profileName", e)
            false
        }
    }
    
    /**
     * Carica profilo di key mapping
     */
    fun loadProfile(profileName: String): Map<String, Any>? {
        return try {
            // TODO: Implementare caricamento profili
            logger.info("📁 Profile loaded: $profileName")
            emptyMap()
        } catch (e: Exception) {
            logger.error("Failed to load profile: $profileName", e)
            null
        }
    }
    
    /**
     * Ottiene statistiche del servizio
     */
    fun getServiceStats(): ServiceStats {
        return ServiceStats(
            isActive = isServiceActive,
            activeAVDCount = activeAVDs.size,
            overlayActive = overlayManager?.isMonitoring() ?: false,
            engineActive = keyMappingEngine?.isRunning() ?: false
        )
    }
}

/**
 * Statistiche del servizio
 */
data class ServiceStats(
    val isActive: Boolean,
    val activeAVDCount: Int,
    val overlayActive: Boolean,
    val engineActive: Boolean
)

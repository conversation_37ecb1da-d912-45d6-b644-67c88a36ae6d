package com.bluestackslike.keymapper.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.bluestackslike.keymapper.core.AVDDetector
import com.bluestackslike.keymapper.core.KeyMappingEngine
import com.bluestackslike.keymapper.ui.components.*
import java.awt.*
import java.awt.event.*
import javax.swing.*

/**
 * Main BlueStacks-like key mapping dialog
 * Provides drag & drop interface exactly like BlueStacks
 */
class KeyMappingDialog(
    private val project: Project,
    private val avdInfo: AVDDetector.AVDInfo
) : DialogWrapper(project) {
    
    private lateinit var keyPalette: KeyPalettePanel
    private lateinit var mappingOverlay: MappingOverlayPanel
    private lateinit var controlPanel: ControlPanel
    private lateinit var previewPanel: AVDPreviewPanel
    
    private val keyMappingEngine = KeyMappingEngine(avdInfo)
    
    init {
        title = "Key Mapping - ${avdInfo.displayName}"
        setSize(1200, 800)
        isResizable = true
        init()
        
        // Start the mapping engine
        keyMappingEngine.start()
    }
    
    override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        
        // Create main components
        createComponents()
        
        // Layout setup
        val leftPanel = createLeftPanel()
        val centerPanel = createCenterPanel()
        val rightPanel = createRightPanel()
        
        mainPanel.add(leftPanel, BorderLayout.WEST)
        mainPanel.add(centerPanel, BorderLayout.CENTER)
        mainPanel.add(rightPanel, BorderLayout.EAST)
        
        return mainPanel
    }
    
    private fun createComponents() {
        // Key palette with all available keys (like BlueStacks)
        keyPalette = KeyPalettePanel { keyType ->
            onKeySelected(keyType)
        }
        
        // AVD preview with overlay for key mapping
        previewPanel = AVDPreviewPanel(avdInfo) { point ->
            onScreenClicked(point)
        }
        
        // Mapping overlay for visual feedback
        mappingOverlay = MappingOverlayPanel(previewPanel)
        
        // Control panel with save/load/clear buttons
        controlPanel = ControlPanel(
            onSave = { saveCurrentMapping() },
            onLoad = { loadMapping() },
            onClear = { clearAllMappings() },
            onTest = { testMappings() }
        )
    }
    
    private fun createLeftPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.preferredSize = Dimension(250, 600)
        panel.border = BorderFactory.createTitledBorder("Key Palette")
        
        // Add key palette
        panel.add(JScrollPane(keyPalette), BorderLayout.CENTER)
        
        // Add control buttons at bottom
        panel.add(controlPanel, BorderLayout.SOUTH)
        
        return panel
    }
    
    private fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = BorderFactory.createTitledBorder("AVD Screen - Drag keys here")
        
        // Create layered pane for overlay functionality
        val layeredPane = JLayeredPane()
        layeredPane.preferredSize = Dimension(600, 400)
        
        // Add preview panel (background)
        previewPanel.bounds = Rectangle(0, 0, 600, 400)
        layeredPane.add(previewPanel, JLayeredPane.DEFAULT_LAYER)
        
        // Add mapping overlay (foreground)
        mappingOverlay.bounds = Rectangle(0, 0, 600, 400)
        mappingOverlay.isOpaque = false
        layeredPane.add(mappingOverlay, JLayeredPane.PALETTE_LAYER)
        
        panel.add(layeredPane, BorderLayout.CENTER)
        
        return panel
    }
    
    private fun createRightPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.preferredSize = Dimension(200, 600)
        panel.border = BorderFactory.createTitledBorder("Mapping List")
        
        // Create mapping list
        val mappingList = JList<String>()
        mappingList.selectionMode = ListSelectionModel.SINGLE_SELECTION
        
        // Add list to panel
        panel.add(JScrollPane(mappingList), BorderLayout.CENTER)
        
        // Add mapping info panel
        val infoPanel = JPanel()
        infoPanel.layout = BoxLayout(infoPanel, BoxLayout.Y_AXIS)
        infoPanel.add(JLabel("Active Mappings: 0"))
        infoPanel.add(JLabel("Profile: Default"))
        
        panel.add(infoPanel, BorderLayout.SOUTH)
        
        return panel
    }
    
    /**
     * Handle key selection from palette
     */
    private fun onKeySelected(keyType: KeyType) {
        // Enable drag mode
        mappingOverlay.startDragMode(keyType)
        
        // Change cursor to indicate drag mode
        previewPanel.cursor = Cursor.getPredefinedCursor(Cursor.CROSSHAIR_CURSOR)
        
        // Show instruction
        showStatusMessage("Click on the screen where you want to place the ${keyType.displayName} key")
    }
    
    /**
     * Handle screen click for key placement
     */
    private fun onScreenClicked(point: Point) {
        val draggedKey = mappingOverlay.getDraggedKey()
        
        if (draggedKey != null) {
            // Create new key mapping
            val mapping = KeyMapping(
                keyType = draggedKey,
                screenPosition = point,
                avdPosition = convertToAVDCoordinates(point)
            )
            
            // Add to engine
            keyMappingEngine.addMapping(mapping)
            
            // Update overlay
            mappingOverlay.addKeyMapping(mapping)
            
            // Reset drag mode
            mappingOverlay.endDragMode()
            previewPanel.cursor = Cursor.getDefaultCursor()
            
            showStatusMessage("${draggedKey.displayName} key mapped to position (${point.x}, ${point.y})")
        }
    }
    
    /**
     * Convert preview coordinates to actual AVD coordinates
     */
    private fun convertToAVDCoordinates(previewPoint: Point): Point {
        val previewBounds = previewPanel.bounds
        val avdBounds = avdInfo.bounds
        
        val scaleX = avdBounds.width.toDouble() / previewBounds.width
        val scaleY = avdBounds.height.toDouble() / previewBounds.height
        
        return Point(
            (previewPoint.x * scaleX).toInt(),
            (previewPoint.y * scaleY).toInt()
        )
    }
    
    private fun saveCurrentMapping() {
        // TODO: Implement save functionality
        showStatusMessage("Mapping saved successfully!")
    }
    
    private fun loadMapping() {
        // TODO: Implement load functionality
        showStatusMessage("Mapping loaded successfully!")
    }
    
    private fun clearAllMappings() {
        keyMappingEngine.clearAllMappings()
        mappingOverlay.clearAllMappings()
        showStatusMessage("All mappings cleared!")
    }
    
    private fun testMappings() {
        // TODO: Implement test functionality
        showStatusMessage("Testing key mappings...")
    }
    
    private fun showStatusMessage(message: String) {
        // TODO: Show status in status bar or notification
        println("Status: $message")
    }
    
    override fun doOKAction() {
        // Save mappings before closing
        keyMappingEngine.saveToProfile("default")
        keyMappingEngine.stop()
        super.doOKAction()
    }
    
    override fun doCancelAction() {
        keyMappingEngine.stop()
        super.doCancelAction()
    }
}

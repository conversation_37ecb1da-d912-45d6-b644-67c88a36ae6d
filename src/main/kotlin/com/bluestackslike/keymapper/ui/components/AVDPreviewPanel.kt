package com.bluestackslike.keymapper.ui.components

import com.bluestackslike.keymapper.core.AVDDetector
import java.awt.*
import java.awt.event.*
import java.awt.image.BufferedImage
import javax.swing.*
import kotlin.math.*

/**
 * AVD preview panel showing scaled version of the actual AVD screen
 * Supports 1:1 scaling and click-to-coordinate mapping
 */
class AVDPreviewPanel(
    private val avdInfo: AVDDetector.AVDInfo,
    private val onScreenClicked: (Point) -> Unit
) : JPanel() {
    
    private var avdScreenshot: BufferedImage? = null
    private var scaleFactor: Double = 1.0
    private var isCapturing = false
    
    init {
        background = Color(0x1E1E1E)
        border = BorderFactory.createLineBorder(Color.GRAY, 1)
        
        addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                onScreenClicked(e.point)
            }
        })
        
        // Start screenshot capture timer
        startScreenCapture()
    }
    
    /**
     * Start capturing AVD screenshots for preview
     */
    private fun startScreenCapture() {
        val timer = Timer(100) { // 10 FPS
            if (!isCapturing) {
                captureAVDScreen()
            }
        }
        timer.start()
    }
    
    /**
     * Capture screenshot of AVD window
     */
    private fun captureAVDScreen() {
        if (isCapturing) return
        
        SwingUtilities.invokeLater {
            try {
                isCapturing = true
                
                // Create robot for screen capture
                val robot = Robot()
                val screenCapture = robot.createScreenCapture(avdInfo.bounds)
                
                // Scale to fit panel
                val scaledImage = scaleImage(screenCapture)
                avdScreenshot = scaledImage
                
                repaint()
                
            } catch (e: Exception) {
                // Fallback to placeholder
                createPlaceholderImage()
            } finally {
                isCapturing = false
            }
        }
    }
    
    /**
     * Scale image to fit panel while maintaining aspect ratio
     */
    private fun scaleImage(originalImage: BufferedImage): BufferedImage {
        val panelWidth = width
        val panelHeight = height
        
        if (panelWidth <= 0 || panelHeight <= 0) {
            return originalImage
        }
        
        val originalWidth = originalImage.width
        val originalHeight = originalImage.height
        
        // Calculate scale factor to fit panel
        val scaleX = panelWidth.toDouble() / originalWidth
        val scaleY = panelHeight.toDouble() / originalHeight
        scaleFactor = min(scaleX, scaleY)
        
        val scaledWidth = (originalWidth * scaleFactor).toInt()
        val scaledHeight = (originalHeight * scaleFactor).toInt()
        
        val scaledImage = BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB)
        val g2d = scaledImage.createGraphics()
        
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR)
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY)
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        
        g2d.drawImage(originalImage, 0, 0, scaledWidth, scaledHeight, null)
        g2d.dispose()
        
        return scaledImage
    }
    
    /**
     * Create placeholder image when AVD is not available
     */
    private fun createPlaceholderImage() {
        val width = max(400, this.width)
        val height = max(600, this.height)
        
        avdScreenshot = BufferedImage(width, height, BufferedImage.TYPE_INT_RGB)
        val g2d = avdScreenshot!!.createGraphics()
        
        // Draw placeholder background
        g2d.color = Color(0x2D2D2D)
        g2d.fillRect(0, 0, width, height)
        
        // Draw phone outline
        g2d.color = Color(0x404040)
        g2d.stroke = BasicStroke(3f)
        g2d.drawRoundRect(20, 20, width - 40, height - 40, 30, 30)
        
        // Draw screen area
        g2d.color = Color.BLACK
        g2d.fillRoundRect(40, 60, width - 80, height - 120, 15, 15)
        
        // Draw Android logo placeholder
        g2d.color = Color(0x3DDC84)
        g2d.font = Font("Arial", Font.BOLD, 24)
        val text = "Android AVD"
        val fm = g2d.fontMetrics
        val textX = (width - fm.stringWidth(text)) / 2
        val textY = height / 2
        g2d.drawString(text, textX, textY)
        
        // Draw instruction text
        g2d.color = Color.GRAY
        g2d.font = Font("Arial", Font.PLAIN, 14)
        val instruction = "Start AVD to see live preview"
        val instFm = g2d.fontMetrics
        val instX = (width - instFm.stringWidth(instruction)) / 2
        val instY = textY + 40
        g2d.drawString(instruction, instX, instY)
        
        g2d.dispose()
    }
    
    override fun paintComponent(g: Graphics) {
        super.paintComponent(g)
        
        val g2d = g as Graphics2D
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        
        if (avdScreenshot != null) {
            // Center the image in the panel
            val imageWidth = avdScreenshot!!.width
            val imageHeight = avdScreenshot!!.height
            val x = (width - imageWidth) / 2
            val y = (height - imageHeight) / 2
            
            g2d.drawImage(avdScreenshot, x, y, null)
            
            // Draw border around image
            g2d.color = Color(0x555555)
            g2d.stroke = BasicStroke(1f)
            g2d.drawRect(x - 1, y - 1, imageWidth + 1, imageHeight + 1)
            
        } else {
            // Show loading message
            g2d.color = Color.GRAY
            g2d.font = Font("Arial", Font.PLAIN, 16)
            val text = "Capturing AVD screen..."
            val fm = g2d.fontMetrics
            val textX = (width - fm.stringWidth(text)) / 2
            val textY = height / 2
            g2d.drawString(text, textX, textY)
        }
        
        // Draw scale info
        drawScaleInfo(g2d)
    }
    
    /**
     * Draw scale information
     */
    private fun drawScaleInfo(g2d: Graphics2D) {
        val scaleText = "Scale: ${String.format("%.1f", scaleFactor * 100)}%"
        val avdText = "AVD: ${avdInfo.displayName}"
        val resText = "Resolution: ${avdInfo.bounds.width}x${avdInfo.bounds.height}"
        
        g2d.color = Color(0, 0, 0, 128)
        g2d.fillRect(5, height - 60, 200, 55)
        
        g2d.color = Color.WHITE
        g2d.font = Font("Arial", Font.PLAIN, 11)
        
        g2d.drawString(avdText, 10, height - 45)
        g2d.drawString(resText, 10, height - 30)
        g2d.drawString(scaleText, 10, height - 15)
    }
    
    /**
     * Convert panel coordinates to AVD coordinates
     */
    fun panelToAVDCoordinates(panelPoint: Point): Point {
        if (avdScreenshot == null) return panelPoint
        
        val imageWidth = avdScreenshot!!.width
        val imageHeight = avdScreenshot!!.height
        val imageX = (width - imageWidth) / 2
        val imageY = (height - imageHeight) / 2
        
        // Convert to image coordinates
        val imagePoint = Point(
            panelPoint.x - imageX,
            panelPoint.y - imageY
        )
        
        // Scale to AVD coordinates
        return Point(
            (imagePoint.x / scaleFactor).toInt(),
            (imagePoint.y / scaleFactor).toInt()
        )
    }
    
    /**
     * Convert AVD coordinates to panel coordinates
     */
    fun avdToPanelCoordinates(avdPoint: Point): Point {
        if (avdScreenshot == null) return avdPoint
        
        val imageWidth = avdScreenshot!!.width
        val imageHeight = avdScreenshot!!.height
        val imageX = (width - imageWidth) / 2
        val imageY = (height - imageHeight) / 2
        
        // Scale from AVD coordinates
        val imagePoint = Point(
            (avdPoint.x * scaleFactor).toInt(),
            (avdPoint.y * scaleFactor).toInt()
        )
        
        // Convert to panel coordinates
        return Point(
            imagePoint.x + imageX,
            imagePoint.y + imageY
        )
    }
    
    override fun getPreferredSize(): Dimension {
        return Dimension(400, 600)
    }
}

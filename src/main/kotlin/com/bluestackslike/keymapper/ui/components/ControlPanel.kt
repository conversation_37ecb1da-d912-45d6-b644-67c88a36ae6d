package com.bluestackslike.keymapper.ui.components

import java.awt.*
import java.awt.event.ActionEvent
import javax.swing.*

/**
 * Control panel with save/load/clear/test buttons
 * Provides BlueStacks-like control functionality
 */
class ControlPanel(
    private val onSave: () -> Unit,
    private val onLoad: () -> Unit,
    private val onClear: () -> Unit,
    private val onTest: () -> Unit
) : JPanel() {
    
    init {
        layout = BoxLayout(this, BoxLayout.Y_AXIS)
        background = Color(0x2B2B2B)
        border = BorderFactory.createEmptyBorder(10, 10, 10, 10)
        
        createControlButtons()
    }
    
    private fun createControlButtons() {
        // Save button
        val saveButton = createStyledButton("💾 Save", Color(0x0078D4)) {
            onSave()
        }
        add(saveButton)
        add(Box.createVerticalStrut(8))
        
        // Load button
        val loadButton = createStyledButton("📁 Load", Color(0x107C10)) {
            onLoad()
        }
        add(loadButton)
        add(Box.createVerticalStrut(8))
        
        // Test button
        val testButton = createStyledButton("🎮 Test", Color(0xFF8C00)) {
            onTest()
        }
        add(testButton)
        add(Box.createVerticalStrut(8))
        
        // Clear button
        val clearButton = createStyledButton("🗑️ Clear", Color(0xD13438)) {
            val result = JOptionPane.showConfirmDialog(
                this,
                "Are you sure you want to clear all key mappings?",
                "Clear Mappings",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE
            )
            
            if (result == JOptionPane.YES_OPTION) {
                onClear()
            }
        }
        add(clearButton)
        
        add(Box.createVerticalStrut(20))
        
        // Profile section
        createProfileSection()
        
        add(Box.createVerticalStrut(20))
        
        // Settings section
        createSettingsSection()
        
        // Push everything to top
        add(Box.createVerticalGlue())
    }
    
    private fun createStyledButton(text: String, color: Color, action: () -> Unit): JButton {
        val button = JButton(text)
        
        button.apply {
            preferredSize = Dimension(180, 35)
            maximumSize = Dimension(180, 35)
            alignmentX = Component.CENTER_ALIGNMENT
            
            background = color
            foreground = Color.WHITE
            font = Font("Arial", Font.BOLD, 12)
            isFocusPainted = false
            border = BorderFactory.createEmptyBorder(8, 16, 8, 16)
            
            addActionListener { action() }
            
            // Hover effects
            addMouseListener(object : java.awt.event.MouseAdapter() {
                override fun mouseEntered(e: java.awt.event.MouseEvent) {
                    background = color.brighter()
                }
                
                override fun mouseExited(e: java.awt.event.MouseEvent) {
                    background = color
                }
            })
        }
        
        return button
    }
    
    private fun createProfileSection() {
        // Profile label
        val profileLabel = JLabel("Profile Management")
        profileLabel.foreground = Color.WHITE
        profileLabel.font = Font("Arial", Font.BOLD, 12)
        profileLabel.alignmentX = Component.CENTER_ALIGNMENT
        add(profileLabel)
        
        add(Box.createVerticalStrut(8))
        
        // Profile dropdown
        val profileCombo = JComboBox(arrayOf("Default", "Gaming", "MOBA", "FPS", "Custom"))
        profileCombo.apply {
            preferredSize = Dimension(180, 30)
            maximumSize = Dimension(180, 30)
            alignmentX = Component.CENTER_ALIGNMENT
            background = Color(0x3C3C3C)
            foreground = Color.WHITE
        }
        add(profileCombo)
        
        add(Box.createVerticalStrut(8))
        
        // Profile buttons
        val profileButtonPanel = JPanel()
        profileButtonPanel.layout = BoxLayout(profileButtonPanel, BoxLayout.X_AXIS)
        profileButtonPanel.background = Color(0x2B2B2B)
        profileButtonPanel.alignmentX = Component.CENTER_ALIGNMENT
        
        val newProfileBtn = createSmallButton("New") {
            createNewProfile()
        }
        val deleteProfileBtn = createSmallButton("Delete") {
            deleteCurrentProfile()
        }
        
        profileButtonPanel.add(newProfileBtn)
        profileButtonPanel.add(Box.createHorizontalStrut(5))
        profileButtonPanel.add(deleteProfileBtn)
        
        add(profileButtonPanel)
    }
    
    private fun createSettingsSection() {
        // Settings label
        val settingsLabel = JLabel("Quick Settings")
        settingsLabel.foreground = Color.WHITE
        settingsLabel.font = Font("Arial", Font.BOLD, 12)
        settingsLabel.alignmentX = Component.CENTER_ALIGNMENT
        add(settingsLabel)
        
        add(Box.createVerticalStrut(8))
        
        // Enable/disable checkbox
        val enabledCheckbox = JCheckBox("Enable Key Mapping", true)
        enabledCheckbox.apply {
            foreground = Color.WHITE
            background = Color(0x2B2B2B)
            alignmentX = Component.CENTER_ALIGNMENT
        }
        add(enabledCheckbox)
        
        add(Box.createVerticalStrut(5))
        
        // Show overlay checkbox
        val overlayCheckbox = JCheckBox("Show Visual Overlay", true)
        overlayCheckbox.apply {
            foreground = Color.WHITE
            background = Color(0x2B2B2B)
            alignmentX = Component.CENTER_ALIGNMENT
        }
        add(overlayCheckbox)
        
        add(Box.createVerticalStrut(5))
        
        // Auto-save checkbox
        val autoSaveCheckbox = JCheckBox("Auto-save Changes", true)
        autoSaveCheckbox.apply {
            foreground = Color.WHITE
            background = Color(0x2B2B2B)
            alignmentX = Component.CENTER_ALIGNMENT
        }
        add(autoSaveCheckbox)
        
        add(Box.createVerticalStrut(10))
        
        // Sensitivity slider
        val sensitivityLabel = JLabel("Mouse Sensitivity")
        sensitivityLabel.foreground = Color.WHITE
        sensitivityLabel.font = Font("Arial", Font.PLAIN, 10)
        sensitivityLabel.alignmentX = Component.CENTER_ALIGNMENT
        add(sensitivityLabel)
        
        val sensitivitySlider = JSlider(1, 10, 5)
        sensitivitySlider.apply {
            preferredSize = Dimension(160, 30)
            maximumSize = Dimension(160, 30)
            alignmentX = Component.CENTER_ALIGNMENT
            background = Color(0x2B2B2B)
            foreground = Color.WHITE
        }
        add(sensitivitySlider)
    }
    
    private fun createSmallButton(text: String, action: () -> Unit): JButton {
        val button = JButton(text)
        
        button.apply {
            preferredSize = Dimension(85, 25)
            maximumSize = Dimension(85, 25)
            
            background = Color(0x404040)
            foreground = Color.WHITE
            font = Font("Arial", Font.PLAIN, 10)
            isFocusPainted = false
            border = BorderFactory.createEmptyBorder(4, 8, 4, 8)
            
            addActionListener { action() }
        }
        
        return button
    }
    
    private fun createNewProfile() {
        val profileName = JOptionPane.showInputDialog(
            this,
            "Enter profile name:",
            "New Profile",
            JOptionPane.PLAIN_MESSAGE
        )
        
        if (!profileName.isNullOrBlank()) {
            // TODO: Create new profile
            JOptionPane.showMessageDialog(
                this,
                "Profile '$profileName' created successfully!",
                "Profile Created",
                JOptionPane.INFORMATION_MESSAGE
            )
        }
    }
    
    private fun deleteCurrentProfile() {
        val result = JOptionPane.showConfirmDialog(
            this,
            "Are you sure you want to delete the current profile?",
            "Delete Profile",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        )
        
        if (result == JOptionPane.YES_OPTION) {
            // TODO: Delete current profile
            JOptionPane.showMessageDialog(
                this,
                "Profile deleted successfully!",
                "Profile Deleted",
                JOptionPane.INFORMATION_MESSAGE
            )
        }
    }
}

package com.bluestackslike.keymapper.ui.components

import java.awt.*
import java.awt.event.*
import javax.swing.*
import javax.swing.border.Border

/**
 * Key palette panel - exactly like BlueStacks key selection
 * Provides draggable keys for mapping
 */
class KeyPalettePanel(
    private val onKeySelected: (KeyType) -> Unit
) : JPanel() {
    
    private val keyButtons = mutableListOf<KeyButton>()
    
    init {
        layout = GridBagLayout()
        background = Color(0x2B2B2B)
        createKeyPalette()
    }
    
    private fun createKeyPalette() {
        val gbc = GridBagConstraints()
        gbc.insets = Insets(5, 5, 5, 5)
        gbc.fill = GridBagConstraints.HORIZONTAL
        
        var row = 0
        
        // Gaming Keys Section
        addSectionHeader("🎮 Gaming Keys", row++)
        
        val gamingKeys = listOf(
            KeyType.WASD_MOVEMENT,
            KeyType.ARROW_MOVEMENT,
            KeyType.MOUSE_LEFT,
            KeyType.MOUSE_RIGHT,
            KeyType.SPACE,
            KeyType.SHIFT,
            KeyType.CTRL,
            KeyType.ALT
        )
        
        gamingKeys.forEachIndexed { index, keyType ->
            if (index % 2 == 0) {
                gbc.gridx = 0
                gbc.gridy = row
            } else {
                gbc.gridx = 1
                if (index == gamingKeys.size - 1) row++
            }
            
            val button = createKeyButton(keyType)
            add(button, gbc)
            
            if (index % 2 == 1 || index == gamingKeys.size - 1) {
                row++
            }
        }
        
        // Alphabet Keys Section
        addSectionHeader("🔤 Alphabet", row++)
        
        val alphabetKeys = ('A'..'Z').map { KeyType.Letter(it) }
        alphabetKeys.chunked(3).forEach { chunk ->
            chunk.forEachIndexed { index, keyType ->
                gbc.gridx = index
                gbc.gridy = row
                
                val button = createKeyButton(keyType)
                add(button, gbc)
            }
            row++
        }
        
        // Number Keys Section
        addSectionHeader("🔢 Numbers", row++)
        
        val numberKeys = (0..9).map { KeyType.Number(it) }
        numberKeys.chunked(5).forEach { chunk ->
            chunk.forEachIndexed { index, keyType ->
                gbc.gridx = index
                gbc.gridy = row
                
                val button = createKeyButton(keyType)
                add(button, gbc)
            }
            row++
        }
        
        // Function Keys Section
        addSectionHeader("⚡ Function Keys", row++)
        
        val functionKeys = (1..12).map { KeyType.Function(it) }
        functionKeys.chunked(4).forEach { chunk ->
            chunk.forEachIndexed { index, keyType ->
                gbc.gridx = index
                gbc.gridy = row
                
                val button = createKeyButton(keyType)
                add(button, gbc)
            }
            row++
        }
        
        // Special Keys Section
        addSectionHeader("⭐ Special", row++)
        
        val specialKeys = listOf(
            KeyType.ENTER,
            KeyType.TAB,
            KeyType.ESC,
            KeyType.BACKSPACE
        )
        
        specialKeys.chunked(2).forEach { chunk ->
            chunk.forEachIndexed { index, keyType ->
                gbc.gridx = index
                gbc.gridy = row
                
                val button = createKeyButton(keyType)
                add(button, gbc)
            }
            row++
        }
    }
    
    private fun addSectionHeader(title: String, row: Int) {
        val gbc = GridBagConstraints()
        gbc.gridx = 0
        gbc.gridy = row
        gbc.gridwidth = 3
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.insets = Insets(10, 5, 5, 5)
        
        val label = JLabel(title)
        label.foreground = Color.WHITE
        label.font = label.font.deriveFont(Font.BOLD, 12f)
        label.border = BorderFactory.createMatteBorder(0, 0, 1, 0, Color.GRAY)
        
        add(label, gbc)
    }
    
    private fun createKeyButton(keyType: KeyType): KeyButton {
        val button = KeyButton(keyType)
        keyButtons.add(button)
        
        // Add click handler
        button.addActionListener {
            onKeySelected(keyType)
            button.isSelected = true
            
            // Deselect other buttons
            keyButtons.filter { it != button }.forEach { it.isSelected = false }
        }
        
        // Add drag support
        addDragSupport(button, keyType)
        
        return button
    }
    
    private fun addDragSupport(button: KeyButton, keyType: KeyType) {
        val dragGestureListener = object : DragGestureListener {
            override fun dragGestureRecognized(dge: DragGestureEvent) {
                // Start drag operation
                onKeySelected(keyType)
                button.isSelected = true
            }
        }
        
        val dragSource = DragSource.getDefaultDragSource()
        dragSource.createDefaultDragGestureRecognizer(
            button,
            DnDConstants.ACTION_COPY,
            dragGestureListener
        )
    }
}

/**
 * Custom button for key palette
 */
class KeyButton(private val keyType: KeyType) : JButton() {
    
    var isSelected: Boolean = false
        set(value) {
            field = value
            updateAppearance()
        }
    
    init {
        text = keyType.displayName
        preferredSize = Dimension(70, 35)
        font = Font("Arial", Font.BOLD, 10)
        isFocusPainted = false
        updateAppearance()
        
        // Add hover effects
        addMouseListener(object : MouseAdapter() {
            override fun mouseEntered(e: MouseEvent) {
                if (!isSelected) {
                    background = Color(0x404040)
                }
            }
            
            override fun mouseExited(e: MouseEvent) {
                if (!isSelected) {
                    updateAppearance()
                }
            }
        })
    }
    
    private fun updateAppearance() {
        if (isSelected) {
            background = Color(0x0078D4)
            foreground = Color.WHITE
            border = BorderFactory.createLineBorder(Color(0x106EBE), 2)
        } else {
            background = Color(0x3C3C3C)
            foreground = Color.WHITE
            border = BorderFactory.createLineBorder(Color(0x555555), 1)
        }
        repaint()
    }
}

/**
 * Key type definitions - all possible keys like BlueStacks
 */
sealed class KeyType(val displayName: String, val keyCode: Int) {
    
    // Gaming keys
    object WASD_MOVEMENT : KeyType("WASD", 0)
    object ARROW_MOVEMENT : KeyType("Arrows", 0)
    object MOUSE_LEFT : KeyType("LMB", 0)
    object MOUSE_RIGHT : KeyType("RMB", 0)
    object SPACE : KeyType("Space", KeyEvent.VK_SPACE)
    object SHIFT : KeyType("Shift", KeyEvent.VK_SHIFT)
    object CTRL : KeyType("Ctrl", KeyEvent.VK_CONTROL)
    object ALT : KeyType("Alt", KeyEvent.VK_ALT)
    
    // Alphabet
    class Letter(val letter: Char) : KeyType(letter.toString(), KeyEvent.getExtendedKeyCodeForChar(letter.code))
    
    // Numbers
    class Number(val number: Int) : KeyType(number.toString(), KeyEvent.VK_0 + number)
    
    // Function keys
    class Function(val number: Int) : KeyType("F$number", KeyEvent.VK_F1 + number - 1)
    
    // Special keys
    object ENTER : KeyType("Enter", KeyEvent.VK_ENTER)
    object TAB : KeyType("Tab", KeyEvent.VK_TAB)
    object ESC : KeyType("Esc", KeyEvent.VK_ESCAPE)
    object BACKSPACE : KeyType("Back", KeyEvent.VK_BACK_SPACE)
}

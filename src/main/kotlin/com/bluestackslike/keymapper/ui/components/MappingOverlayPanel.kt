package com.bluestackslike.keymapper.ui.components

import java.awt.*
import java.awt.event.*
import javax.swing.*
import kotlin.math.*

/**
 * Overlay panel for visual key mapping feedback
 * Shows mapped keys as visual indicators on the AVD screen
 */
class MappingOverlayPanel(
    private val parentPanel: JComponent
) : JPanel() {
    
    private val keyMappings = mutableListOf<KeyMapping>()
    private var draggedKey: KeyType? = null
    private var isDragMode = false
    private var mousePosition: Point? = null
    
    init {
        isOpaque = false
        cursor = Cursor.getDefaultCursor()
        
        addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                if (isDragMode && draggedKey != null) {
                    // This will be handled by parent
                    parentPanel.dispatchEvent(e)
                }
            }
            
            override fun mousePressed(e: MouseEvent) {
                // Check if clicking on existing mapping for editing
                val clickedMapping = findMappingAt(e.point)
                if (clickedMapping != null) {
                    showMappingContextMenu(clickedMapping, e.point)
                }
            }
        })
        
        addMouseMotionListener(object : MouseMotionAdapter() {
            override fun mouseMoved(e: MouseEvent) {
                if (isDragMode) {
                    mousePosition = e.point
                    repaint()
                }
            }
        })
    }
    
    /**
     * Start drag mode for key placement
     */
    fun startDragMode(keyType: KeyType) {
        draggedKey = keyType
        isDragMode = true
        cursor = Cursor.getPredefinedCursor(Cursor.CROSSHAIR_CURSOR)
        repaint()
    }
    
    /**
     * End drag mode
     */
    fun endDragMode() {
        draggedKey = null
        isDragMode = false
        mousePosition = null
        cursor = Cursor.getDefaultCursor()
        repaint()
    }
    
    /**
     * Get currently dragged key
     */
    fun getDraggedKey(): KeyType? = draggedKey
    
    /**
     * Add a new key mapping
     */
    fun addKeyMapping(mapping: KeyMapping) {
        keyMappings.add(mapping)
        repaint()
    }
    
    /**
     * Remove a key mapping
     */
    fun removeKeyMapping(mapping: KeyMapping) {
        keyMappings.remove(mapping)
        repaint()
    }
    
    /**
     * Clear all mappings
     */
    fun clearAllMappings() {
        keyMappings.clear()
        repaint()
    }
    
    /**
     * Find mapping at specific point
     */
    private fun findMappingAt(point: Point): KeyMapping? {
        return keyMappings.find { mapping ->
            val bounds = getKeyBounds(mapping)
            bounds.contains(point)
        }
    }
    
    /**
     * Get bounds for a key mapping
     */
    private fun getKeyBounds(mapping: KeyMapping): Rectangle {
        val size = getKeySize(mapping.keyType)
        return Rectangle(
            mapping.screenPosition.x - size.width / 2,
            mapping.screenPosition.y - size.height / 2,
            size.width,
            size.height
        )
    }
    
    /**
     * Get size for different key types
     */
    private fun getKeySize(keyType: KeyType): Dimension {
        return when (keyType) {
            is KeyType.WASD_MOVEMENT -> Dimension(80, 80)
            is KeyType.ARROW_MOVEMENT -> Dimension(80, 80)
            is KeyType.MOUSE_LEFT, is KeyType.MOUSE_RIGHT -> Dimension(50, 50)
            else -> Dimension(40, 40)
        }
    }
    
    /**
     * Show context menu for editing mappings
     */
    private fun showMappingContextMenu(mapping: KeyMapping, point: Point) {
        val popup = JPopupMenu()
        
        popup.add(JMenuItem("Edit").apply {
            addActionListener {
                editMapping(mapping)
            }
        })
        
        popup.add(JMenuItem("Delete").apply {
            addActionListener {
                removeKeyMapping(mapping)
            }
        })
        
        popup.add(JMenuItem("Duplicate").apply {
            addActionListener {
                duplicateMapping(mapping)
            }
        })
        
        popup.show(this, point.x, point.y)
    }
    
    private fun editMapping(mapping: KeyMapping) {
        // TODO: Show edit dialog
        println("Editing mapping: ${mapping.keyType.displayName}")
    }
    
    private fun duplicateMapping(mapping: KeyMapping) {
        val newMapping = mapping.copy(
            screenPosition = Point(
                mapping.screenPosition.x + 20,
                mapping.screenPosition.y + 20
            )
        )
        addKeyMapping(newMapping)
    }
    
    override fun paintComponent(g: Graphics) {
        super.paintComponent(g)
        
        val g2d = g as Graphics2D
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        
        // Draw existing key mappings
        drawKeyMappings(g2d)
        
        // Draw drag preview
        if (isDragMode && mousePosition != null && draggedKey != null) {
            drawDragPreview(g2d)
        }
    }
    
    /**
     * Draw all existing key mappings
     */
    private fun drawKeyMappings(g2d: Graphics2D) {
        keyMappings.forEach { mapping ->
            drawKeyMapping(g2d, mapping, false)
        }
    }
    
    /**
     * Draw drag preview
     */
    private fun drawDragPreview(g2d: Graphics2D) {
        val pos = mousePosition ?: return
        val key = draggedKey ?: return
        
        val tempMapping = KeyMapping(
            keyType = key,
            screenPosition = pos,
            avdPosition = pos // Temporary
        )
        
        drawKeyMapping(g2d, tempMapping, true)
    }
    
    /**
     * Draw individual key mapping
     */
    private fun drawKeyMapping(g2d: Graphics2D, mapping: KeyMapping, isPreview: Boolean) {
        val pos = mapping.screenPosition
        val size = getKeySize(mapping.keyType)
        val bounds = Rectangle(
            pos.x - size.width / 2,
            pos.y - size.height / 2,
            size.width,
            size.height
        )
        
        // Set colors based on key type and preview state
        val (bgColor, borderColor, textColor) = getKeyColors(mapping.keyType, isPreview)
        
        // Draw background
        g2d.color = bgColor
        g2d.fillRoundRect(bounds.x, bounds.y, bounds.width, bounds.height, 8, 8)
        
        // Draw border
        g2d.color = borderColor
        g2d.stroke = BasicStroke(if (isPreview) 2f else 1f)
        g2d.drawRoundRect(bounds.x, bounds.y, bounds.width, bounds.height, 8, 8)
        
        // Draw key text
        g2d.color = textColor
        g2d.font = Font("Arial", Font.BOLD, 12)
        
        val fm = g2d.fontMetrics
        val text = mapping.keyType.displayName
        val textWidth = fm.stringWidth(text)
        val textHeight = fm.height
        
        val textX = bounds.x + (bounds.width - textWidth) / 2
        val textY = bounds.y + (bounds.height + textHeight) / 2 - fm.descent
        
        g2d.drawString(text, textX, textY)
        
        // Draw special indicators for movement keys
        if (mapping.keyType is KeyType.WASD_MOVEMENT) {
            drawWASDIndicator(g2d, bounds)
        } else if (mapping.keyType is KeyType.ARROW_MOVEMENT) {
            drawArrowIndicator(g2d, bounds)
        }
    }
    
    /**
     * Get colors for different key types
     */
    private fun getKeyColors(keyType: KeyType, isPreview: Boolean): Triple<Color, Color, Color> {
        val alpha = if (isPreview) 128 else 200
        
        return when (keyType) {
            is KeyType.WASD_MOVEMENT, is KeyType.ARROW_MOVEMENT -> 
                Triple(
                    Color(0, 120, 215, alpha),
                    Color(0, 90, 180),
                    Color.WHITE
                )
            is KeyType.MOUSE_LEFT, is KeyType.MOUSE_RIGHT ->
                Triple(
                    Color(220, 50, 50, alpha),
                    Color(180, 30, 30),
                    Color.WHITE
                )
            else ->
                Triple(
                    Color(80, 80, 80, alpha),
                    Color(120, 120, 120),
                    Color.WHITE
                )
        }
    }
    
    /**
     * Draw WASD movement indicator
     */
    private fun drawWASDIndicator(g2d: Graphics2D, bounds: Rectangle) {
        val centerX = bounds.x + bounds.width / 2
        val centerY = bounds.y + bounds.height / 2
        val size = 8
        
        g2d.color = Color.WHITE
        g2d.font = Font("Arial", Font.BOLD, 8)
        
        // Draw W, A, S, D in cross pattern
        g2d.drawString("W", centerX - 3, centerY - 8)
        g2d.drawString("A", centerX - 12, centerY + 3)
        g2d.drawString("S", centerX - 3, centerY + 12)
        g2d.drawString("D", centerX + 6, centerY + 3)
    }
    
    /**
     * Draw arrow movement indicator
     */
    private fun drawArrowIndicator(g2d: Graphics2D, bounds: Rectangle) {
        val centerX = bounds.x + bounds.width / 2
        val centerY = bounds.y + bounds.height / 2
        val size = 6
        
        g2d.color = Color.WHITE
        g2d.stroke = BasicStroke(2f)
        
        // Draw arrow cross
        // Up arrow
        g2d.drawLine(centerX, centerY - size, centerX - 3, centerY - size + 3)
        g2d.drawLine(centerX, centerY - size, centerX + 3, centerY - size + 3)
        
        // Down arrow
        g2d.drawLine(centerX, centerY + size, centerX - 3, centerY + size - 3)
        g2d.drawLine(centerX, centerY + size, centerX + 3, centerY + size - 3)
        
        // Left arrow
        g2d.drawLine(centerX - size, centerY, centerX - size + 3, centerY - 3)
        g2d.drawLine(centerX - size, centerY, centerX - size + 3, centerY + 3)
        
        // Right arrow
        g2d.drawLine(centerX + size, centerY, centerX + size - 3, centerY - 3)
        g2d.drawLine(centerX + size, centerY, centerX + size - 3, centerY + 3)
    }
}

/**
 * Data class for key mappings
 */
data class KeyMapping(
    val keyType: KeyType,
    val screenPosition: Point,
    val avdPosition: Point,
    val id: String = java.util.UUID.randomUUID().toString()
)

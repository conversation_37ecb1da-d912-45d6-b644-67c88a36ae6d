package com.bluestackslike.keymapper.utils

import com.intellij.notification.*
import com.intellij.openapi.project.Project

/**
 * Utility class for showing notifications in Android Studio
 */
object NotificationUtils {
    
    private val notificationGroup = NotificationGroupManager.getInstance()
        .getNotificationGroup("KeyMapper.Notifications")
    
    /**
     * Show info notification
     */
    fun showInfo(project: Project, title: String, content: String) {
        showNotification(project, title, content, NotificationType.INFORMATION)
    }
    
    /**
     * Show warning notification
     */
    fun showWarning(project: Project, title: String, content: String) {
        showNotification(project, title, content, NotificationType.WARNING)
    }
    
    /**
     * Show error notification
     */
    fun showError(project: Project, title: String, content: String) {
        showNotification(project, title, content, NotificationType.ERROR)
    }
    
    /**
     * Show notification with custom type
     */
    private fun showNotification(
        project: Project,
        title: String,
        content: String,
        type: NotificationType
    ) {
        val notification = notificationGroup.createNotification(title, content, type)
        notification.notify(project)
    }
    
    /**
     * Show balloon notification at specific location
     */
    fun showBalloon(
        project: Project,
        title: String,
        content: String,
        type: NotificationType = NotificationType.INFORMATION
    ) {
        val notification = notificationGroup.createNotification(title, content, type)
        
        // Add action buttons if needed
        notification.addAction(object : NotificationAction("Dismiss") {
            override fun actionPerformed(e: com.intellij.openapi.actionSystem.AnActionEvent, notification: Notification) {
                notification.expire()
            }
        })
        
        notification.notify(project)
    }
}
